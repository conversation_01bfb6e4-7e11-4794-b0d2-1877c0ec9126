{"name": "capehorn-monitor", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start"}, "dependencies": {"@radix-ui/react-avatar": "latest", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-progress": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "geist": "latest", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "latest", "react": "^19", "react-dom": "^19", "react-is": "^19.1.1", "react-resizable-panels": "^2.1.7", "recharts": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.9", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8.5", "tailwindcss": "^4.1.9", "tw-animate-css": "1.3.3", "typescript": "^5"}}