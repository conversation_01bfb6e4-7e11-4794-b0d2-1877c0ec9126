# Capehorn Monitor

*Network and transaction monitoring dashboard*

[![Deployed on Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-black?style=for-the-badge&logo=vercel)](https://vercel.com/rickypins-projects/v0-ai-agent-ui)
[![Built with v0](https://img.shields.io/badge/Built%20with-v0.app-black?style=for-the-badge)](https://v0.app/chat/projects/oPn2G7l8g2t)

## Overview

Capehorn Monitor is a comprehensive network and transaction monitoring dashboard that provides real-time insights into VISA service performance, network health indicators, and transaction health metrics.

## Features

- **Real-time Monitoring**: Live network and transaction health indicators
- **VISA Service Integration**: Specialized monitoring for VISA payment services
- **Interactive Dashboard**: Comprehensive charts and metrics visualization
- **Multi-language Support**: English and Chinese interface options
- **Responsive Design**: Optimized for desktop and mobile viewing

## Getting Started

1. Install dependencies: `npm install`
2. Run the development server: `npm run dev`
3. Open [http://localhost:3000](http://localhost:3000) in your browser
4. Navigate to the monitor dashboard to view real-time metrics
