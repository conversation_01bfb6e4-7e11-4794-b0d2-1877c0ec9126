# Border Radius Guidelines

## 🎯 Overview

This project uses **Tailwind CSS standard rounded classes** for all border radius needs. No custom CSS variables are required.

## ✅ Recommended Usage

### Standard Tailwind Classes

| Class | Value | Usage |
|-------|-------|-------|
| `rounded-none` | 0px | No rounding |
| `rounded-sm` | 2px | Minimal rounding |
| `rounded` | 4px | **Buttons, forms, badges** |
| `rounded-md` | 6px | Medium elements |
| `rounded-lg` | 8px | **Cards, containers** |
| `rounded-xl` | 12px | **Large containers** |
| `rounded-2xl` | 16px | **Special containers** |
| `rounded-3xl` | 24px | Hero sections (rare) |
| `rounded-4xl` | 32px | Special cases (rare) |
| `rounded-full` | 9999px | **Avatars, circular buttons** |

### Component Guidelines

```tsx
// ✅ Recommended patterns
<Button className="rounded">Standard Button</Button>
<Card className="rounded-lg">Content Card</Card>
<Avatar className="rounded-full">User Avatar</Avatar>
<Badge className="rounded">Status Badge</Badge>
<Input className="rounded">Form Input</Input>

// ✅ Large containers
<Modal className="rounded-xl">Modal Dialog</Modal>
<Panel className="rounded-2xl">Dashboard Panel</Panel>

// ✅ Directional rounding
<div className="rounded-t-lg rounded-b-none">Header</div>
<div className="rounded-l-lg rounded-r-none">Sidebar</div>
```

## 🚫 What NOT to Use

### Avoid Custom CSS Variables
```css
/* ❌ Don't define custom radius variables */
--radius-custom: 10px;
--border-radius-special: 14px;
```

### Avoid Hardcoded Values
```css
/* ❌ Don't use hardcoded border-radius */
.custom-component {
  border-radius: 10px;
}
```

### Avoid Inline Styles
```tsx
/* ❌ Don't use inline border-radius */
<div style={{ borderRadius: '8px' }}>Content</div>
```

## 📋 Migration Strategy

### From Custom Variables to Tailwind

If you find custom radius variables, replace them:

```tsx
// ❌ Before (custom variables)
<div className="corner-sm">Content</div>
<div style={{ borderRadius: 'var(--radius-custom)' }}>Content</div>

// ✅ After (Tailwind standard)
<div className="rounded">Content</div>
<div className="rounded-lg">Content</div>
```

## 🎨 Design Consistency

### Size Hierarchy
- **Small elements** (buttons, badges): `rounded` (4px)
- **Medium elements** (cards, inputs): `rounded-lg` (8px)  
- **Large elements** (modals, panels): `rounded-xl` (12px)
- **Special cases** (hero sections): `rounded-2xl` (16px)
- **Circular elements** (avatars): `rounded-full`

### Visual Harmony
- Use consistent rounding within component families
- Larger components should have proportionally larger radius
- Avoid mixing too many different radius sizes in one view

## 🔧 Implementation Notes

### IDE Support
- Full IntelliSense support for Tailwind classes
- Automatic completion and validation
- Built-in documentation on hover

### Performance
- No custom CSS variables to process
- Standard Tailwind classes are optimized
- Smaller bundle size

### Maintenance
- No custom radius system to maintain
- Standard Tailwind documentation applies
- Easy for new developers to understand

## 📚 References

- [Tailwind CSS Border Radius](https://tailwindcss.com/docs/border-radius)
- [Material Design Corner Radius](https://m3.material.io/styles/shape/shape-scale-tokens)
- [Design System Best Practices](https://designsystem.digital.gov/design-tokens/spacing-units/)
