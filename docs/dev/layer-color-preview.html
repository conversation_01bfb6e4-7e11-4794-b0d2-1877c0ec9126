<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitor Layer Color System Preview</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 40px;
        }
        
        .theme-toggle {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .toggle-btn {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .layers-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .layer-preview {
            border-radius: 8px;
            padding: 24px;
            border: 2px solid;
            transition: all 0.3s ease;
        }
        
        .layer-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            font-size: 20px;
            font-weight: 600;
        }
        
        .layer-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .layer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
        }
        
        .metric-card {
            background: rgba(255, 255, 255, 0.7);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 6px;
            padding: 16px;
        }
        
        .metric-title {
            font-weight: 500;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
        }
        
        .metric-label {
            font-size: 12px;
            opacity: 0.7;
        }
        
        /* Light Mode Colors */
        .light-mode .layer-1 {
            background: #fefcf3;
            border-color: #f3e8ab;
            color: #92400e;
        }
        
        .light-mode .layer-1 .layer-icon {
            color: #d97706;
        }
        
        .light-mode .layer-2 {
            background: #f8fafc;
            border-color: #cbd5e1;
            color: #475569;
        }
        
        .light-mode .layer-2 .layer-icon {
            color: #64748b;
        }
        
        .light-mode .layer-3 {
            background: #faf9ff;
            border-color: #e0e7ff;
            color: #4338ca;
        }
        
        .light-mode .layer-3 .layer-icon {
            color: #6366f1;
        }
        
        /* Dark Mode Colors */
        .dark-mode {
            background: #1a1a1a;
            color: #e5e5e5;
        }
        
        .dark-mode .layer-1 {
            background: #1c1917;
            border-color: #44403c;
            color: #fbbf24;
        }
        
        .dark-mode .layer-1 .layer-icon {
            color: #f59e0b;
        }
        
        .dark-mode .layer-2 {
            background: #0f172a;
            border-color: #334155;
            color: #94a3b8;
        }
        
        .dark-mode .layer-2 .layer-icon {
            color: #64748b;
        }
        
        .dark-mode .layer-3 {
            background: #1e1b4b;
            border-color: #3730a3;
            color: #a5b4fc;
        }
        
        .dark-mode .layer-3 .layer-icon {
            color: #6366f1;
        }
        
        .dark-mode .metric-card {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 40px;
        }
        
        .comparison-section {
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        
        .comparison-title {
            font-weight: 600;
            margin-bottom: 16px;
            text-align: center;
        }
        
        .dark-mode .comparison-section {
            border-color: #444;
            background: rgba(255, 255, 255, 0.02);
        }
    </style>
</head>
<body class="light-mode">
    <div class="container">
        <h1>Monitor Layer Color System Preview</h1>
        
        <div class="theme-toggle">
            <button class="toggle-btn" onclick="toggleTheme()">Toggle Dark Mode</button>
        </div>
        
        <div class="layers-grid">
            <div class="layer-preview layer-1">
                <div class="layer-header">
                    <div class="layer-icon">📊</div>
                    Layer 1: Transaction Processing Health
                </div>
                <div class="layer-content">
                    <div class="metric-card">
                        <div class="metric-title">Requests/sec</div>
                        <div class="metric-value">286.00</div>
                        <div class="metric-label">Current Rate</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">Success Rate</div>
                        <div class="metric-value">99.50%</div>
                        <div class="metric-label">Last 15 min</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">Response P95</div>
                        <div class="metric-value">360.5ms</div>
                        <div class="metric-label">95th Percentile</div>
                    </div>
                </div>
            </div>
            
            <div class="layer-preview layer-2">
                <div class="layer-header">
                    <div class="layer-icon">📡</div>
                    Layer 2: Network Transmission Health
                </div>
                <div class="layer-content">
                    <div class="metric-card">
                        <div class="metric-title">End-to-End Latency</div>
                        <div class="metric-value">140ms</div>
                        <div class="metric-label">Average RTT</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">Packet Loss</div>
                        <div class="metric-value">0.1%</div>
                        <div class="metric-label">Loss Rate</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">Connections</div>
                        <div class="metric-value">320</div>
                        <div class="metric-label">Active</div>
                    </div>
                </div>
            </div>
            
            <div class="layer-preview layer-3">
                <div class="layer-header">
                    <div class="layer-icon">🔍</div>
                    Layer 3: Cross-Layer Correlation Diagnostics
                </div>
                <div class="layer-content">
                    <div class="metric-card">
                        <div class="metric-title">Success vs Latency</div>
                        <div class="metric-value">Normal</div>
                        <div class="metric-label">Correlation</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">Network Impact</div>
                        <div class="metric-value">Low</div>
                        <div class="metric-label">On Performance</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">Root Cause</div>
                        <div class="metric-value">Network</div>
                        <div class="metric-label">Primary Factor</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="comparison">
            <div class="comparison-section">
                <div class="comparison-title">✅ Benefits of New System</div>
                <ul>
                    <li><strong>Clear Visual Hierarchy:</strong> Each layer has distinct color theme</li>
                    <li><strong>Semantic Safety:</strong> No confusion with status colors</li>
                    <li><strong>Functional Association:</strong> Colors hint at layer purpose</li>
                    <li><strong>Professional Aesthetics:</strong> Enterprise-suitable palette</li>
                    <li><strong>Accessibility:</strong> Good contrast in both themes</li>
                </ul>
            </div>
            
            <div class="comparison-section">
                <div class="comparison-title">🎨 Color Themes</div>
                <ul>
                    <li><strong>Layer 1 (Amber):</strong> Warm, active, processing energy</li>
                    <li><strong>Layer 2 (Slate):</strong> Cool, stable, infrastructure</li>
                    <li><strong>Layer 3 (Indigo):</strong> Analytical, intelligent, correlation</li>
                </ul>
                <p><small>All themes avoid red/green/yellow monitoring semantics</small></p>
            </div>
        </div>
    </div>
    
    <script>
        function toggleTheme() {
            const body = document.body;
            const btn = document.querySelector('.toggle-btn');
            
            if (body.classList.contains('light-mode')) {
                body.classList.remove('light-mode');
                body.classList.add('dark-mode');
                btn.textContent = 'Toggle Light Mode';
            } else {
                body.classList.remove('dark-mode');
                body.classList.add('light-mode');
                btn.textContent = 'Toggle Dark Mode';
            }
        }
    </script>
</body>
</html>
